#include "CompletionItem.h"

namespace Lsp {

QJsonObject CompletionItem::toJson() const {
    QJsonObject obj;
    obj["label"] = label;
    if (kind.has_value()) {
        obj["kind"] = static_cast<int>(kind.value());
    }
    if (detail.has_value()) {
        obj["detail"] = detail.value();
    }
    if (documentation.has_value()) {
        const QVariant& doc = documentation.value();
        if (doc.userType() == QMetaType::QString) {
            obj["documentation"] = doc.toString();
        } else if (doc.canConvert<MarkupContent>()) {
            obj["documentation"] = doc.value<MarkupContent>().toJson();
        }
    }
    if (deprecated.has_value()) {
        obj["deprecated"] = deprecated.value();
    }
    if (preselect.has_value()) {
        obj["preselect"] = preselect.value();
    }
    if (sortText.has_value()) {
        obj["sortText"] = sortText.value();
    }
    if (filterText.has_value()) {
        obj["filterText"] = filterText.value();
    }
    if (insertText.has_value()) {
        obj["insertText"] = insertText.value();
    }
    if (insertTextFormat.has_value()) {
        obj["insertTextFormat"] = insertTextFormat.value();
    }
    if (textEdit.has_value()) {
        obj["textEdit"] = textEdit.value();
    }
    if (additionalTextEdits.has_value()) {
        obj["additionalTextEdits"] = additionalTextEdits.value();
    }
    if (commitCharacters.has_value()) {
        QJsonArray arr;
        for (const QString& ch : commitCharacters.value()) {
            arr.append(ch);
        }
        obj["commitCharacters"] = arr;
    }
    if (command.has_value()) {
        obj["command"] = command.value();
    }
    if (data.has_value()) {
        obj["data"] = data.value();
    }
    return obj;
}

CompletionItem CompletionItem::fromJson(const QJsonObject& json) {
    CompletionItem item;
    item.label = json["label"].toString();
    if (json.contains("kind")) {
        item.kind = static_cast<CompletionItemKind>(json["kind"].toInt());
    }
    if (json.contains("detail")) {
        item.detail = json["detail"].toString();
    }
    if (json.contains("documentation")) {
        item.documentation = json["documentation"].toString();
    }
    if (json.contains("deprecated")) {
        item.deprecated = json["deprecated"].toBool();
    }
    if (json.contains("preselect")) {
        item.preselect = json["preselect"].toBool();
    }
    if (json.contains("sortText")) {
        item.sortText = json["sortText"].toString();
    }
    if (json.contains("filterText")) {
        item.filterText = json["filterText"].toString();
    }
    if (json.contains("insertText")) {
        item.insertText = json["insertText"].toString();
    }
    if (json.contains("insertTextFormat")) {
        item.insertTextFormat = json["insertTextFormat"];
    }
    if (json.contains("textEdit")) {
        item.textEdit = json["textEdit"];
    }
    if (json.contains("additionalTextEdits")) {
        item.additionalTextEdits = json["additionalTextEdits"].toArray();
    }
    if (json.contains("commitCharacters")) {
        QStringList chars;
        QJsonArray arr = json["commitCharacters"].toArray();
        for (const QJsonValue& val : arr) {
            chars.append(val.toString());
        }
        item.commitCharacters = chars;
    }
    if (json.contains("command")) {
        item.command = json["command"];
    }
    if (json.contains("data")) {
        item.data = json["data"];
    }
    return item;
}

} // namespace Lsp
