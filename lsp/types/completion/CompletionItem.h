#ifndef LSP_TYPES_COMPLETION_COMPLETIONITEM_H
#define LSP_TYPES_COMPLETION_COMPLETIONITEM_H

#include "../LspTypesCommon.h"
#include "../MarkupContent.h"
#include "CompletionItemKind.h"

namespace Lsp {

/**
 * Represents a completion item
 */
struct CompletionItem {
    QString label;
    std::optional<CompletionItemKind> kind;
    std::optional<QString> detail;
    std::optional<QString> documentation;
    std::optional<bool> deprecated;
    std::optional<bool> preselect;
    std::optional<QString> sortText;
    std::optional<QString> filterText;
    std::optional<QString> insertText;
    std::optional<QJsonValue> insertTextFormat;
    std::optional<QJsonValue> textEdit;
    std::optional<QJsonArray> additionalTextEdits;
    std::optional<QStringList> commitCharacters;
    std::optional<QJsonValue> command;
    std::optional<QJsonValue> data;

    CompletionItem() = default;
    CompletionItem(const QString& l) : label(l) {}

    QJsonObject toJson() const;
    static CompletionItem fromJson(const QJsonObject& json);
};

} // namespace Lsp

#endif // LSP_TYPES_COMPLETION_COMPLETIONITEM_H
