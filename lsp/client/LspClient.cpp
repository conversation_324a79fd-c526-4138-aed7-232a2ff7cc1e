#include "LspClient.h"
#include <QJsonParseError>
#include <QDebug>
#include <QDir>
#include <QUrl>
#include <QStandardPaths>
#include <QMutexLocker>
#include <QCoreApplication>

LspClient::LspClient(QObject *parent)
  : QObject(parent), m_lspProcess(new QProcess(this)), m_messageId(0)
{
    // Connect QProcess signals to the appropriate slots
    connect(m_lspProcess, &QProcess::readyReadStandardOutput, this, &LspClient::onReadyReadStandardOutput);
    connect(m_lspProcess, &QProcess::readyReadStandardError, this, &LspClient::onReadyReadStandardError);
    connect(m_lspProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished), this, &LspClient::onProcessFinished);
}

LspClient::~LspClient()
{
    stopServer();
}

void LspClient::startServer(const QString &command, const QStringList &arguments, const QString &workingDirectory)
{
    if (m_lspProcess->state() == QProcess::NotRunning) {
        if (!workingDirectory.isEmpty()) {
            m_lspProcess->setWorkingDirectory(workingDirectory);
        }
        m_lspProcess->start(command, arguments);
        m_lspProcess->waitForStarted();

        if (m_lspProcess->state() == QProcess::Running) {
            emit serverStarted();
        } else {
            emit serverError(m_lspProcess->exitCode(), m_lspProcess->exitStatus());
        }
    }
}

void LspClient::stopServer()
{
    if (m_lspProcess->state() == QProcess::Running) {
        m_lspProcess->terminate();
        m_lspProcess->waitForFinished(3000); // Wait 3 seconds for graceful shutdown
        if (m_lspProcess->state() == QProcess::Running) {
            m_lspProcess->kill(); // Force kill if it doesn't terminate
        }
    }
}

int LspClient::sendRequest(const QString &method, const QJsonObject &params)
{
    QJsonObject request;
    request["jsonrpc"] = "2.0";
    request["id"] = ++m_messageId;
    request["method"] = method;
    request["params"] = params;

    sendMessage(request);
    return m_messageId;
}

void LspClient::sendNotification(const QString &method, const QJsonObject &params)
{
    QJsonObject notification;
    notification["jsonrpc"] = "2.0";
    notification["method"] = method;
    notification["params"] = params;

    sendMessage(notification);
}

QVariant LspClient::sendMessage(std::unique_ptr<Lsp::LspMessage> message)
{
    if (m_lspProcess->state() != QProcess::Running) {
        qWarning() << "LSP server is not running.";
        return QVariant();
    }

    QVariant messageId;

    // Track requests for response matching
    if (message->isRequest()) {
        auto request = dynamic_cast<Lsp::LspRequest*>(message.get());
        if (request) {
            messageId = request->getId();
            m_messageRegistry.registerPendingRequest(messageId, request->getMethod());
        }
    }

    // Serialize and send the message
    QByteArray messageBytes = Lsp::LspMessageFactory::serializeMessage(*message);
    m_lspProcess->write(messageBytes);

    return messageId;
}

int LspClient::initialize(const Lsp::InitializeParams& params)
{
    auto request = std::make_unique<Lsp::InitializeRequest>(++m_messageId, params);
    QVariant id = sendMessage(std::move(request));
    return id.toInt();
}

void LspClient::sendInitialized()
{
    auto notification = std::make_unique<Lsp::InitializedNotification>();
    sendMessage(std::move(notification));
}

int LspClient::shutdown()
{
    auto request = std::make_unique<Lsp::ShutdownRequest>(++m_messageId);
    QVariant id = sendMessage(std::move(request));
    return id.toInt();
}

void LspClient::sendExit()
{
    auto notification = std::make_unique<Lsp::ExitNotification>();
    sendMessage(std::move(notification));
}

void LspClient::didOpenTextDocument(const Lsp::TextDocumentItem& textDocument)
{
    Lsp::DidOpenNotification::DidOpenTextDocumentParams params;
    params.textDocument = textDocument;
    auto notification = std::make_unique<Lsp::DidOpenNotification>(params);
    sendMessage(std::move(notification));
}

void LspClient::didChangeTextDocument(const Lsp::VersionedTextDocumentIdentifier& textDocument,
                                     const QList<Lsp::TextDocumentContentChangeEvent>& contentChanges)
{
    Lsp::DidChangeNotification::DidChangeTextDocumentParams params;
    params.textDocument = textDocument;
    params.contentChanges = contentChanges;
    auto notification = std::make_unique<Lsp::DidChangeNotification>(params);
    sendMessage(std::move(notification));
}

void LspClient::didSaveTextDocument(const Lsp::TextDocumentIdentifier& textDocument,
                                   const std::optional<QString>& text)
{
    Lsp::DidSaveNotification::DidSaveTextDocumentParams params;
    params.textDocument = textDocument;
    params.text = text;
    auto notification = std::make_unique<Lsp::DidSaveNotification>(params);
    sendMessage(std::move(notification));
}

void LspClient::didCloseTextDocument(const Lsp::TextDocumentIdentifier& textDocument)
{
    Lsp::DidCloseNotification::DidCloseTextDocumentParams params;
    params.textDocument = textDocument;
    auto notification = std::make_unique<Lsp::DidCloseNotification>(params);
    sendMessage(std::move(notification));
}

int LspClient::requestCompletion(const Lsp::TextDocumentIdentifier& textDocument, const Lsp::Position& position)
{
    Lsp::CompletionParams params;
    params.textDocument = textDocument;
    params.position = position;
    auto request = std::make_unique<Lsp::CompletionRequest>(++m_messageId, params);
    QVariant id = sendMessage(std::move(request));
    return id.toInt();
}

void LspClient::sendMessage(const QJsonObject &message)
{
    if (m_lspProcess->state() != QProcess::Running) {
        qWarning() << "LSP server is not running.";
        return;
    }

    QJsonDocument doc(message);
    QByteArray messageBytes = doc.toJson(QJsonDocument::Compact);
    QByteArray header = "Content-Length: " + QByteArray::number(messageBytes.size()) + "\r\n\r\n";

    m_lspProcess->write(header + messageBytes);
}

void LspClient::onReadyReadStandardOutput()
{
    m_buffer.append(m_lspProcess->readAllStandardOutput());
    parseResponse(m_buffer);
}

void LspClient::onReadyReadStandardError()
{
    qWarning() << "LSP Server stderr:" << m_lspProcess->readAllStandardError();
}


void LspClient::parseResponse(const QByteArray &data)
{
    while (!m_buffer.isEmpty()) {
        int bytesConsumed = 0;
        auto message = m_messageFactory.parseMessage(m_buffer, bytesConsumed, &m_messageRegistry);

        if (!message) {
            // Not enough data for a complete message
            break;
        }

        // Remove consumed bytes from buffer
        m_buffer.remove(0, bytesConsumed);

        // Process the message
        processMessage(std::move(message));
    }
}

void LspClient::processMessage(std::unique_ptr<Lsp::LspMessage> message)
{
    // Get raw pointer for signal emission (keep ownership in this function)
    Lsp::LspMessage* rawMessage = message.get();
    emit messageReceived(rawMessage);

    if (message->isResponse()) {
        auto response = dynamic_cast<Lsp::LspResponse*>(rawMessage);
        if (response) {
            QVariant id = response->getId();
            QString method = m_messageRegistry.getPendingRequestMethod(id);
            m_messageRegistry.removePendingRequest(id);

            // Handle specific response types with proper type safety
            if (method == "initialize") {
                auto initResponse = dynamic_cast<Lsp::InitializeResponse*>(rawMessage);
                if (initResponse) {
                    emit initialized(initResponse->getInitializeResult());
                }
            } else if (method == "textDocument/completion") {
                auto completionResponse = dynamic_cast<Lsp::CompletionResponse*>(rawMessage);
                if (completionResponse) {
                    emit completionReceived(id.toInt(), completionResponse->getCompletionList());
                }
            }

            // Emit generic response signal for backward compatibility
            QJsonObject result = response->getResult().toObject();
            emit responseReceived(id.toInt(), result);
        }
    } else if (message->isError()) {
        auto error = dynamic_cast<Lsp::LspError*>(rawMessage);
        if (error) {
            QVariant id = error->getId();
            m_messageRegistry.removePendingRequest(id);

            // Convert error to JSON for backward compatibility
            QJsonObject errorObj = error->getError().toJson();
            emit errorReceived(id.toInt(), errorObj);
        }
    } else if (message->isNotification()) {
        auto notification = dynamic_cast<Lsp::LspNotification*>(rawMessage);
        if (notification) {
            QString method = notification->getMethod();

            // Handle specific notification types
            if (method == "textDocument/didOpen") {
                auto didOpen = dynamic_cast<Lsp::DidOpenNotification*>(rawMessage);
                if (didOpen) {
                    emit textDocumentOpened(didOpen->getDidOpenParams());
                }
            } else if (method == "textDocument/didChange") {
                auto didChange = dynamic_cast<Lsp::DidChangeNotification*>(rawMessage);
                if (didChange) {
                    emit textDocumentChanged(didChange->getDidChangeParams());
                }
            } else if (method == "textDocument/didSave") {
                auto didSave = dynamic_cast<Lsp::DidSaveNotification*>(rawMessage);
                if (didSave) {
                    emit textDocumentSaved(didSave->getDidSaveParams());
                }
            } else if (method == "textDocument/didClose") {
                auto didClose = dynamic_cast<Lsp::DidCloseNotification*>(rawMessage);
                if (didClose) {
                    emit textDocumentClosed(didClose->getDidCloseParams());
                }
            }

            // Emit generic notification signal for backward compatibility
            QJsonObject params = notification->getParams();
            emit notificationReceived(method, params);
        }
    }
}


void LspClient::onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    emit serverError(exitCode, exitStatus);
}
