#include "CodeEditor.h"

#include "ContentEngine.h"
#include "PythonEngine.h"
#include "ILexer.h"
#include "Lexilla.h"
#include "MainWindow.h"
#include "SciLexer.h"
#include "lsp/client/LspClient.h"

#include <QApplication>
#include <QMenu>
#include <QStyleHints>
#include <QRegularExpression>
#include <QString>
#include <QStringLiteral>
using namespace Qt::Literals::StringLiterals;

constexpr auto MARKER_MARKDOWN_LINE           = 1;
constexpr auto MARKER_EXECUTION_STATUS        = 2;
constexpr auto MASK_MARKDOWN_LINE             = 1 << MARKER_MARKDOWN_LINE;
constexpr auto MASK_EXECUTION_STATUS          = 1 << MARKER_EXECUTION_STATUS;
constexpr auto MARGIN_NUMBER_LINENUMBER       = 0;
constexpr auto MARGIN_NUMBER_EXECUTION_STATUS = 1;

//#define FOLD_ENABLED
#ifdef FOLD_ENABLED
constexpr auto MARGIN_NUMBER_FOLD             = 2;
constexpr auto NUMBER_OF_MARGINS              = 3;
#else
constexpr auto NUMBER_OF_MARGINS              = 2;
#endif

constexpr auto STYLE_ANNOTATION_MEMORY    = STYLE_LASTPREDEFINED + 1;
constexpr auto STYLE_ANNOTATION_ERROR     = STYLE_LASTPREDEFINED + 2;
constexpr auto STYLE_EXECUTION_NONE       = STYLE_LASTPREDEFINED + 3;
constexpr auto STYLE_EXECUTION_REQUESTED  = STYLE_LASTPREDEFINED + 4;
constexpr auto STYLE_EXECUTION_EXECUTING  = STYLE_LASTPREDEFINED + 5;
constexpr auto STYLE_EXECUTION_DONE_OK    = STYLE_LASTPREDEFINED + 6;
constexpr auto STYLE_EXECUTION_DONE_ERROR = STYLE_LASTPREDEFINED + 7;
constexpr auto STYLE_CODE_COMPLETION      = STYLE_LASTPREDEFINED + 8;

QCodeEditor::QCodeEditor( const enumCodeType codeType, QWidget* parent )
    : ScintillaEdit{ parent }
    , m_codeType{ codeType }
    , m_lspClient{ nullptr }
    , m_pendingCompletionRequestId{ -1 }
{
    const auto styleHints  = QGuiApplication::styleHints();
    const auto colorScheme = styleHints->colorScheme();

    // context menu
    usePopUp( SC_POPUP_NEVER ); // turn off scintilla
    setContextMenuPolicy( Qt::CustomContextMenu );
    connect( this, &QCodeEditor::customContextMenuRequested, this, &QCodeEditor::showContextMenu );

    // lexer
    setLexer( codeType );
    setLanguageParsingColoring( codeType, colorScheme );

    // setup annotation: description of what line of code did
    const auto nAnnotationSize = styleSizeFractional( STYLE_ANNOTATION_MEMORY ) * 0.75;
    styleSetFont( STYLE_ANNOTATION_MEMORY, FONT_NAME_CODE );
    styleSetItalic( STYLE_ANNOTATION_MEMORY, true );
    styleSetEOLFilled( STYLE_ANNOTATION_MEMORY, true );
    styleSetSizeFractional( STYLE_ANNOTATION_MEMORY, nAnnotationSize );
    styleSetFont( STYLE_ANNOTATION_ERROR, FONT_NAME_CODE );
    styleSetItalic( STYLE_ANNOTATION_ERROR, true );
    styleSetEOLFilled( STYLE_ANNOTATION_ERROR, true );
    styleSetSizeFractional( STYLE_ANNOTATION_ERROR, nAnnotationSize );

    styleSetFont( STYLE_CODE_COMPLETION, FONT_NAME_CODE );

    constexpr auto lineSpacing = int{ 6 };
    setExtraAscent( lineSpacing );
    setExtraDescent( lineSpacing );
    setWrapMode( SC_WRAP_WORD );
    setMemoryVisible( true );

    constexpr auto marginSize = 6;
    setMarginLeft( marginSize );
    setMarginRight( marginSize );

    // marker for line type and execution status
    markerDefine( MARKER_MARKDOWN_LINE, SC_MARK_BACKGROUND );
    markerDefine( MARKER_EXECUTION_STATUS, SC_MARK_EMPTY );

    // margins for line number and execution status
    setMargins( NUMBER_OF_MARGINS );
    setMarginTypeN( MARGIN_NUMBER_LINENUMBER, SC_MARGIN_NUMBER );
    setMarginTypeN( MARGIN_NUMBER_EXECUTION_STATUS, SC_MARGIN_TEXT );
    setMarginMaskN( MARGIN_NUMBER_EXECUTION_STATUS, MASK_EXECUTION_STATUS );

    #ifdef FOLD_ENABLED
    setMarginTypeN( MARGIN_NUMBER_FOLD, SC_MARGIN_SYMBOL );
    setMarginMaskN( MARGIN_NUMBER_FOLD, SC_MASK_FOLDERS );
    setMarginWidthN( MARGIN_NUMBER_FOLD, 20 );
    markerDefine( SC_MARKNUM_FOLDEROPEN, SC_MARK_CIRCLEMINUS );
    markerDefine( SC_MARKNUM_FOLDER, SC_MARK_CIRCLEPLUS );
    markerDefine( SC_MARKNUM_FOLDERSUB, SC_MARK_VLINE );
    markerDefine( SC_MARKNUM_FOLDERTAIL, SC_MARK_LCORNERCURVE );
    markerDefine( SC_MARKNUM_FOLDEREND, SC_MARK_CIRCLEPLUSCONNECTED );
    markerDefine( SC_MARKNUM_FOLDEROPENMID, SC_MARK_CIRCLEMINUSCONNECTED );
    markerDefine( SC_MARKNUM_FOLDERMIDTAIL, SC_MARK_TCORNERCURVE );
    setAutomaticFold( SC_AUTOMATICFOLD_SHOW | SC_AUTOMATICFOLD_CHANGE | SC_AUTOMATICFOLD_CLICK );
    #endif

    // margin widths
    const auto widthLineNumber = textWidth( STYLE_LINENUMBER, "99" ); // initial, adapts as the document grows
    setMarginWidthN( MARGIN_NUMBER_LINENUMBER, widthLineNumber );
    const auto widthExecutionStatus = textWidth( STYLE_LINENUMBER, "XXX" );
    setMarginWidthN( MARGIN_NUMBER_EXECUTION_STATUS, widthExecutionStatus );

    auto mainWindow = QTrySailMainWindow::getTrysailMainWindow();
    connect( mainWindow->actionEditCut,            &QAction::triggered, this, &QCodeEditor::onEditCut );
    connect( mainWindow->actionEditCopy,           &QAction::triggered, this, &QCodeEditor::onEditCopy );
    connect( mainWindow->actionEditPaste,          &QAction::triggered, this, &QCodeEditor::onEditPaste );
    connect( mainWindow->actionEditSelectAll,      &QAction::triggered, this, &QCodeEditor::onEditSelectAll );
    connect( mainWindow->actionEditZoomActual,     &QAction::triggered, this, &QCodeEditor::onZoomActual );
    connect( mainWindow->actionEditZoomIn,         &QAction::triggered, this, &QCodeEditor::onZoomIn );
    connect( mainWindow->actionEditZoomOut,        &QAction::triggered, this, &QCodeEditor::onZoomOut );
    connect( mainWindow->actionEditToggleCellType, &QAction::triggered, this, &QCodeEditor::onEditToggleCellType );

    connect( this, &ScintillaEditBase::linesAdded, this, &QCodeEditor::onLinesAdded );
    connect( this, &ScintillaEditBase::charAdded,  this, &QCodeEditor::onCharAdded );
    connect( this, &ScintillaEditBase::notify,     this, &QCodeEditor::onNotify );
    connect( this, &ScintillaEditBase::autoCompleteSelection, this, &QCodeEditor::onAutoCompleteSelection );
    connect( this, &ScintillaEditBase::autoCompleteSelectionChange, this, &QCodeEditor::onAutoCompleteSelectionChange );

    connect( styleHints, &QStyleHints::colorSchemeChanged, this, &QCodeEditor::onColorSchemeChanged );
}

constexpr auto PYTHON_INDENT_SPACES = 4;

constexpr int colorQtToScintilla( const QRgba64& rgbQt )
{
    const auto r = qRed( rgbQt );
    const auto g = qGreen( rgbQt );
    const auto b = qBlue( rgbQt );
    return Scintilla::Internal::ColourRGBA{ r, g, b }.AsInteger();
}

void QCodeEditor::setLanguageParsingColoring( const enumCodeType codeType, const Qt::ColorScheme colorScheme )
{
    constexpr auto sci_solar_base03    = colorQtToScintilla( solar_base03 );
    constexpr auto sci_solar_base02    = colorQtToScintilla( solar_base02 );
    constexpr auto sci_solar_base01    = colorQtToScintilla( solar_base01 );
    constexpr auto sci_solar_base00    = colorQtToScintilla( solar_base00 );
    constexpr auto sci_solar_base0     = colorQtToScintilla( solar_base0 );
    constexpr auto sci_solar_base1     = colorQtToScintilla( solar_base1 );
    constexpr auto sci_solar_base2     = colorQtToScintilla( solar_base2 );
    constexpr auto sci_solar_base3     = colorQtToScintilla( solar_base3 );
    constexpr auto sci_solar_yellow    = colorQtToScintilla( solar_yellow );
    constexpr auto sci_solar_orange    = colorQtToScintilla( solar_orange );
    constexpr auto sci_solar_red       = colorQtToScintilla( solar_red );
    constexpr auto sci_solar_magenta   = colorQtToScintilla( solar_magenta );
    constexpr auto sci_solar_violet    = colorQtToScintilla( solar_violet );
    constexpr auto sci_solar_blue      = colorQtToScintilla( solar_blue );
    constexpr auto sci_solar_cyan      = colorQtToScintilla( solar_cyan );
    constexpr auto sci_solar_green     = colorQtToScintilla( solar_green );

    const     auto bLight                  = (colorScheme != Qt::ColorScheme::Dark );
    const     auto colorBackgroundCode     = bLight ? sci_solar_base3  : sci_solar_base03;
    const     auto colorBackgroundMarkdown = bLight ? sci_solar_base2  : sci_solar_base02;
    const     auto colorDefaultFore        = bLight ? sci_solar_base00 : sci_solar_base0;
    const     auto colorBackgroundMargin   = colorBackgroundCode;
    constexpr auto colorComment            = sci_solar_green;
    constexpr auto colorNumber             = sci_solar_orange;
    constexpr auto colorString             = sci_solar_cyan;
    constexpr auto colorKeyword            = sci_solar_green;
    constexpr auto colorOperator           = sci_solar_violet;
    constexpr auto colorIdentifier         = sci_solar_blue;
    constexpr auto colorLink               = sci_solar_blue;
    constexpr auto colorError              = sci_solar_red;

    styleSetBack( STYLE_DEFAULT, colorBackgroundCode );
    styleSetFore( STYLE_DEFAULT, colorDefaultFore    );

    setElementColour( SC_ELEMENT_CARET,            bLight ? sci_solar_base03 : sci_solar_base3 );
    setElementColour( SC_ELEMENT_WHITE_SPACE,      colorBackgroundCode );
    setElementColour( SC_ELEMENT_WHITE_SPACE_BACK, colorBackgroundCode );

    styleSetBack( STYLE_LINENUMBER, colorBackgroundMargin );
    styleSetFore( STYLE_LINENUMBER, bLight ? sci_solar_base01 : sci_solar_base1 );

    styleSetBack( STYLE_ANNOTATION_MEMORY, colorBackgroundCode );
    styleSetFore( STYLE_ANNOTATION_MEMORY, bLight ? sci_solar_base01 : sci_solar_base1 );
    styleSetBack( STYLE_ANNOTATION_ERROR, colorBackgroundCode );
    styleSetFore( STYLE_ANNOTATION_ERROR, sci_solar_red );

    markerSetBack( MARKER_MARKDOWN_LINE,      colorBackgroundMarkdown );

    styleSetFore( STYLE_EXECUTION_NONE,       colorBackgroundMargin );
    styleSetFore( STYLE_EXECUTION_REQUESTED,  sci_solar_yellow );
    styleSetFore( STYLE_EXECUTION_EXECUTING,  sci_solar_cyan );
    styleSetFore( STYLE_EXECUTION_DONE_OK,    sci_solar_green );
    styleSetFore( STYLE_EXECUTION_DONE_ERROR, sci_solar_red );
    styleSetBack( STYLE_EXECUTION_NONE,       colorBackgroundMargin );
    styleSetBack( STYLE_EXECUTION_REQUESTED,  colorBackgroundMargin );
    styleSetBack( STYLE_EXECUTION_EXECUTING,  colorBackgroundMargin );
    styleSetBack( STYLE_EXECUTION_DONE_OK,    colorBackgroundMargin );
    styleSetBack( STYLE_EXECUTION_DONE_ERROR, colorBackgroundMargin );

    switch ( codeType ) {

    case enumCodeType::codeTypePython:
        setProperty( "fold", "1" );
        setProperty( "fold.quotes.python", "1" );
        setTabIndents( true );
        setBackSpaceUnIndents( true );
        setTabWidth( PYTHON_INDENT_SPACES );
        setIndent( 0 );
        setUseTabs( false );
        //setIndentationGuides( SC_IV_LOOKFORWARD );
        setViewWS( SCWS_VISIBLEONLYININDENT );
        setStyleColorFont( SCE_P_DEFAULT,       colorBackgroundCode, colorDefaultFore );        // White space
        setStyleColorFont( SCE_P_COMMENTLINE,   colorBackgroundCode, colorComment );      // Comment
        setStyleColorFont( SCE_P_NUMBER,        colorBackgroundCode, colorNumber );       // Number
        setStyleColorFont( SCE_P_STRING,        colorBackgroundCode, colorString );         // String
        setStyleColorFont( SCE_P_CHARACTER,     colorBackgroundCode, colorString );        // Single quoted string
        setStyleColorFont( SCE_P_WORD,          colorBackgroundCode, colorKeyword ); // Keyword
        setStyleColorFont( SCE_P_TRIPLE,        colorBackgroundCode, colorString );        // Triple quotes
        setStyleColorFont( SCE_P_TRIPLEDOUBLE,  colorBackgroundCode, colorString );        // Triple double quotes
        setStyleColorFont( SCE_P_CLASSNAME,     colorBackgroundCode, colorIdentifier );        // Class name definition
        setStyleColorFont( SCE_P_DEFNAME,       colorBackgroundCode, colorIdentifier );        // Function/method def
        setStyleColorFont( SCE_P_OPERATOR,      colorBackgroundCode, colorOperator );        // Operators
        setStyleColorFont( SCE_P_IDENTIFIER,    colorBackgroundCode, colorIdentifier );     // Identifiers"
        setStyleColorFont( SCE_P_COMMENTBLOCK,  colorBackgroundCode, colorComment );      // Comment-blocks
        setStyleColorFont( SCE_P_STRINGEOL,     colorBackgroundCode, colorString );        // EOL where string noclosed
        setStyleColorFont( SCE_P_WORD2,         colorBackgroundCode, colorKeyword );        // Highlighted identifiers
        setStyleColorFont( SCE_P_DECORATOR,     colorBackgroundCode, colorOperator );        // Decorators
        setStyleColorFont( SCE_P_FSTRING,       colorBackgroundCode, colorString );         // F-String
        setStyleColorFont( SCE_P_FCHARACTER,    colorBackgroundCode, colorString );         // Single quoted f-string
        setStyleColorFont( SCE_P_FTRIPLE,       colorBackgroundCode, colorString );         // Triple quoted f-string
        setStyleColorFont( SCE_P_FTRIPLEDOUBLE, colorBackgroundCode, colorString );         // Triple double quoted f-string
        setStyleColorFont( SCE_P_ATTRIBUTE,     colorBackgroundCode, colorIdentifier );        // Attribute of identifier
        break;

    case enumCodeType::codeTypeSql:
        setStyleColorFont( SCE_SQL_DEFAULT,                colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_SQL_COMMENT,                colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_SQL_COMMENTLINE,            colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_SQL_COMMENTDOC,             colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_SQL_NUMBER,                 colorBackgroundCode, colorNumber );
        setStyleColorFont( SCE_SQL_WORD,                   colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_SQL_STRING,                 colorBackgroundCode, colorString );
        setStyleColorFont( SCE_SQL_CHARACTER,              colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_SQL_SQLPLUS,                colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_SQL_SQLPLUS_PROMPT,         colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_SQL_OPERATOR,               colorBackgroundCode, colorOperator );
        setStyleColorFont( SCE_SQL_IDENTIFIER,             colorBackgroundCode, colorIdentifier );
        setStyleColorFont( SCE_SQL_SQLPLUS_COMMENT,        colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_SQL_COMMENTLINEDOC,         colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_SQL_WORD2,                  colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_SQL_COMMENTDOCKEYWORD,      colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_SQL_COMMENTDOCKEYWORDERROR, colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_SQL_USER1,                  colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_SQL_USER2,                  colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_SQL_USER3,                  colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_SQL_USER4,                  colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_SQL_QUOTEDIDENTIFIER,       colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_SQL_QOPERATOR,              colorBackgroundCode, colorOperator );
        break;

    case enumCodeType::codeTypeMarkdown:
        setStyleColorFont( SCE_MARKDOWN_DEFAULT,    colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_MARKDOWN_LINE_BEGIN, colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_MARKDOWN_STRONG1,    colorBackgroundCode, sci_solar_green );
        setStyleColorFont( SCE_MARKDOWN_STRONG2,    colorBackgroundCode, sci_solar_green );
        setStyleColorFont( SCE_MARKDOWN_EM1,        colorBackgroundCode, sci_solar_green );
        setStyleColorFont( SCE_MARKDOWN_EM2,        colorBackgroundCode, sci_solar_green );
        setStyleColorFont( SCE_MARKDOWN_HEADER1,    colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_MARKDOWN_HEADER2,    colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_MARKDOWN_HEADER3,    colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_MARKDOWN_HEADER4,    colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_MARKDOWN_HEADER5,    colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_MARKDOWN_HEADER6,    colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_MARKDOWN_PRECHAR,    colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_MARKDOWN_ULIST_ITEM, colorBackgroundCode, sci_solar_orange );
        setStyleColorFont( SCE_MARKDOWN_OLIST_ITEM, colorBackgroundCode, sci_solar_orange );
        setStyleColorFont( SCE_MARKDOWN_BLOCKQUOTE, colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_MARKDOWN_STRIKEOUT,  colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_MARKDOWN_HRULE,      colorBackgroundCode, sci_solar_magenta );
        setStyleColorFont( SCE_MARKDOWN_LINK,       colorBackgroundCode, colorLink );
        setStyleColorFont( SCE_MARKDOWN_CODE,       colorBackgroundCode, sci_solar_green );
        setStyleColorFont( SCE_MARKDOWN_CODE2,      colorBackgroundCode, sci_solar_green );
        setStyleColorFont( SCE_MARKDOWN_CODEBK,     colorBackgroundCode, sci_solar_green );
        break;

    case enumCodeType::codeTypeBash:
        setStyleColorFont( SCE_SH_DEFAULT,     colorBackgroundCode, colorDefaultFore ); // White space
        setStyleColorFont( SCE_SH_ERROR,       colorBackgroundCode, sci_solar_orange ); // Error
        setStyleColorFont( SCE_SH_COMMENTLINE, colorBackgroundCode, colorComment ); //Line comment:
        setStyleColorFont( SCE_SH_NUMBER,      colorBackgroundCode, colorNumber ); //"Number
        setStyleColorFont( SCE_SH_WORD,        colorBackgroundCode, colorKeyword ); //Keyword
        setStyleColorFont( SCE_SH_STRING,      colorBackgroundCode, colorString ); //String
        setStyleColorFont( SCE_SH_CHARACTER,   colorBackgroundCode, colorString ); //Single quoted string
        setStyleColorFont( SCE_SH_OPERATOR,    colorBackgroundCode, colorOperator ); //Operators
        setStyleColorFont( SCE_SH_IDENTIFIER,  colorBackgroundCode, colorIdentifier ); //Identifiers
        setStyleColorFont( SCE_SH_SCALAR,      colorBackgroundCode, colorNumber ); //Scalar variable
        setStyleColorFont( SCE_SH_PARAM,       colorBackgroundCode, colorKeyword ); //Parameter
        setStyleColorFont( SCE_SH_BACKTICKS,   colorBackgroundCode, colorKeyword ); //Backtick quoted command
        setStyleColorFont( SCE_SH_HERE_DELIM,  colorBackgroundCode, colorKeyword ); //Heredoc delimiter
        setStyleColorFont( SCE_SH_HERE_Q,      colorBackgroundCode, colorKeyword ); //Heredoc quoted string
        break;

    case enumCodeType::codeTypeJson:
        setStyleColorFont( SCE_JSON_DEFAULT,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_JSON_NUMBER,     colorBackgroundCode, colorNumber );
        setStyleColorFont( SCE_JSON_STRING,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_JSON_STRINGEOL,     colorBackgroundCode, colorString);
        setStyleColorFont( SCE_JSON_PROPERTYNAME,     colorBackgroundCode, colorIdentifier );
        setStyleColorFont( SCE_JSON_ESCAPESEQUENCE,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_JSON_LINECOMMENT,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_JSON_BLOCKCOMMENT,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_JSON_OPERATOR,     colorBackgroundCode, colorOperator );
        setStyleColorFont( SCE_JSON_URI,     colorBackgroundCode, colorLink );
        setStyleColorFont( SCE_JSON_COMPACTIRI,     colorBackgroundCode, colorLink );
        setStyleColorFont( SCE_JSON_KEYWORD,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_JSON_LDKEYWORD,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_JSON_ERROR,     colorBackgroundCode, sci_solar_orange );
        break;

    case enumCodeType::codeTypeHtml:
        setStyleColorFont( SCE_H_DEFAULT,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_H_TAG,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_TAGUNKNOWN,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_ATTRIBUTE,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_ATTRIBUTEUNKNOWN,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_NUMBER,     colorBackgroundCode, colorNumber );
        setStyleColorFont( SCE_H_DOUBLESTRING,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_H_SINGLESTRING,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_H_OTHER,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_H_COMMENT,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_H_ENTITY,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_TAGEND,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_XMLSTART,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_XMLEND,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_SCRIPT,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_ASP,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_ASPAT,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_CDATA,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_QUESTION,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_VALUE,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_XCCOMMENT,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_H_SGML_DEFAULT,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_H_SGML_COMMAND,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_SGML_1ST_PARAM,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_SGML_DOUBLESTRING,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_H_SGML_SIMPLESTRING,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_H_SGML_ERROR,     colorBackgroundCode, sci_solar_orange );
        setStyleColorFont( SCE_H_SGML_SPECIAL,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_SGML_ENTITY,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_H_SGML_COMMENT,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_H_SGML_1ST_PARAM_COMMENT,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_H_SGML_BLOCK_DEFAULT,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_HJ_START,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HJ_DEFAULT,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_HJ_COMMENT,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_HJ_COMMENTLINE,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_HJ_COMMENTDOC,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_HJ_NUMBER,     colorBackgroundCode, colorNumber );
        setStyleColorFont( SCE_HJ_WORD,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HJ_KEYWORD,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HJ_DOUBLESTRING,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HJ_SINGLESTRING,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HJ_SYMBOLS,     colorBackgroundCode, colorOperator );
        setStyleColorFont( SCE_HJ_STRINGEOL,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HJ_REGEX,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HJ_TEMPLATELITERAL,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HJA_START,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_HJA_DEFAULT,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_HJA_COMMENT,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_HJA_COMMENTLINE,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_HJA_COMMENTDOC,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_HJA_NUMBER,     colorBackgroundCode, colorNumber );
        setStyleColorFont( SCE_HJA_WORD,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HJA_KEYWORD,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HJA_DOUBLESTRING,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HJA_SINGLESTRING,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HJA_SYMBOLS,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HJA_STRINGEOL,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HJA_REGEX,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HJA_TEMPLATELITERAL,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HB_START,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_HB_DEFAULT,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_HB_COMMENTLINE,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_HB_NUMBER,     colorBackgroundCode, colorNumber );
        setStyleColorFont( SCE_HB_WORD,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HB_STRING,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HB_IDENTIFIER,     colorBackgroundCode, colorIdentifier );
        setStyleColorFont( SCE_HB_STRINGEOL,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HBA_START,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_HBA_DEFAULT,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_HBA_COMMENTLINE,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_HBA_NUMBER,     colorBackgroundCode, colorNumber );
        setStyleColorFont( SCE_HBA_WORD,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HBA_STRING,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HBA_IDENTIFIER,     colorBackgroundCode, colorIdentifier );
        setStyleColorFont( SCE_HBA_STRINGEOL,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HP_START,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_HP_DEFAULT,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_HP_COMMENTLINE,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_HP_NUMBER,     colorBackgroundCode, colorNumber );
        setStyleColorFont( SCE_HP_STRING,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HP_CHARACTER,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HP_WORD,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HP_TRIPLE,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HP_TRIPLEDOUBLE,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HP_CLASSNAME,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HP_DEFNAME,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HP_OPERATOR,     colorBackgroundCode, colorOperator );
        setStyleColorFont( SCE_HP_IDENTIFIER,     colorBackgroundCode, colorIdentifier );
        setStyleColorFont( SCE_HPHP_COMPLEX_VARIABLE,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HPA_START,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_HPA_DEFAULT,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_HPA_COMMENTLINE,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_HPA_NUMBER,     colorBackgroundCode, colorNumber );
        setStyleColorFont( SCE_HPA_STRING,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HPA_CHARACTER,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HPA_WORD,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HPA_TRIPLE,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HPA_TRIPLEDOUBLE,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HPA_CLASSNAME,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HPA_DEFNAME,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HPA_OPERATOR,     colorBackgroundCode, colorOperator );
        setStyleColorFont( SCE_HPA_IDENTIFIER,     colorBackgroundCode, colorIdentifier );
        setStyleColorFont( SCE_HPHP_DEFAULT,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_HPHP_HSTRING,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HPHP_SIMPLESTRING,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HPHP_WORD,     colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_HPHP_NUMBER,     colorBackgroundCode, colorNumber );
        setStyleColorFont( SCE_HPHP_VARIABLE,     colorBackgroundCode, colorIdentifier );
        setStyleColorFont( SCE_HPHP_COMMENT,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_HPHP_COMMENTLINE,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_HPHP_HSTRING_VARIABLE,     colorBackgroundCode, colorString );
        setStyleColorFont( SCE_HPHP_OPERATOR,     colorBackgroundCode, colorOperator );
        break;

    case enumCodeType::codeTypeBatch:
        setStyleColorFont( SCE_BAT_DEFAULT,     colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_BAT_COMMENT,     colorBackgroundCode, colorComment );
        setStyleColorFont( SCE_BAT_WORD,        colorBackgroundCode, colorKeyword );
        setStyleColorFont( SCE_BAT_LABEL,       colorBackgroundCode, colorString );
        setStyleColorFont( SCE_BAT_HIDE,        colorBackgroundCode, colorDefaultFore );
        setStyleColorFont( SCE_BAT_COMMAND,     colorBackgroundCode, colorNumber );
        setStyleColorFont( SCE_BAT_IDENTIFIER,  colorBackgroundCode, colorIdentifier );
        setStyleColorFont( SCE_BAT_OPERATOR,    colorBackgroundCode, colorOperator );
        setStyleColorFont( SCE_BAT_AFTER_LABEL, colorBackgroundCode, colorDefaultFore );
        break;

    case enumCodeType::codeTypeText:
    default:
        break;
    }
}

void QCodeEditor::onColorSchemeChanged( Qt::ColorScheme colorScheme )
{
    setLanguageParsingColoring( m_codeType, colorScheme );
}

void QCodeEditor::setFilePath( const QString& filePath )
{
    m_filePath = filePath;
}

void QCodeEditor::initializeLsp( const QString& workspaceRoot )
{
    if ( m_codeType != enumCodeType::codeTypePython ) {
        return; // Only initialize LSP for Python files
    }

    if ( !m_lspClient ) {
        m_lspClient = std::make_unique<LspClient>( this );

        // Connect LSP signals
        connect( m_lspClient.get(), &LspClient::pythonLspReady, this, &QCodeEditor::onLspReady );
        connect( m_lspClient.get(), &LspClient::pythonCompletionReceived, this, &QCodeEditor::onLspCompletionReceived);
        connect( m_lspClient.get(), &LspClient::pythonLspError, this, &QCodeEditor::onLspError );

        // Initialize the Python LSP server
        m_lspClient->initializePythonLsp( workspaceRoot );
    }
}

void QCodeEditor::onProgressMemoryUpdate( const QCborMap& cborMemory, const QStringList listFunctions )
{
    m_memory = cborMemory;
    m_listFunctions = listFunctions;
}

void QCodeEditor::onNotify( Scintilla::NotificationData* pscn )
{
    if ( pscn == nullptr )
        return;

    if ( (pscn->modificationType & Scintilla::ModificationFlags::Container ) == Scintilla::ModificationFlags::Container ) {

        if ( ( ( pscn->modificationType & Scintilla::ModificationFlags::Undo ) == Scintilla::ModificationFlags::Undo ) ||
             ( ( pscn->modificationType & Scintilla::ModificationFlags::Redo ) == Scintilla::ModificationFlags::Redo ) ) {

            toggleLineType( pscn->token, false );
        }
    }
}

void QCodeEditor::setStyleColorFont( const sptr_t style, const int colorBack, const int colorFore )
{
    styleSetBack( style, colorBack );
    styleSetFore( style, colorFore );
    styleSetFont( style, FONT_NAME_CODE );
}

constexpr char const* langFromCodeType( const enumCodeType typeCode )
{
    switch ( typeCode ) {
    case enumCodeType::codeTypePython:
        return "python";
        break;
    case enumCodeType::codeTypeSql:
        return "sql";
        break;
    case enumCodeType::codeTypeMarkdown:
        return "markdown";
        break;
    case enumCodeType::codeTypeBash:
        return "bash";
        break;
    case enumCodeType::codeTypeHtml:
        return "hypertext";
        break;
    case enumCodeType::codeTypeJson:
        return "json";
        break;
    case enumCodeType::codeTypeBatch:
        return "batch";
        break;
    case enumCodeType::codeTypeText:
    default:
        return "null";
        break;
    }
}

constexpr auto lstKeywordsPython = "False\nTrue\nNone\nand\nas\nassert\nasync\nawait\ndef\ndel\n"
                               "elif\nelse\nbreak\nclass\ncontinue\nexcept\nfinally\n"
                               "for\nfrom\nglobal\nif\nimport\nin\nis\nlambda\nnonlocal\n"
                               "not\nor\npass\nraise\nreturn\ntry\nwhile\nwith\nyield\n";

constexpr auto lstKeywordsSql = "ABORT\nACTION\nADD\nAFTER\nALL\nALTER\nALWAYS\nANALYZE\nAND\nAS\nASC\n"
                                "ATTACH\nAUTOINCREMENT\nBEFORE\nBEGIN\nBETWEEN\nBY\nCASCADE\nCASE\n"
                                "CAST\nCHECK\nCOLLATE\nCOLUMN\nCOMMIT\nCONFLICT\nCONSTRAINT\nCREATE\n"
                                "CROSS\nCURRENT\nCURRENT_DATE\nCURRENT_TIME\nCURRENT_TIMESTAMP\nDATABASE\n"
                                "DEFAULT\nDEFERRABLE\nDEFERRED\nDELETE\nDESC\nDETACH\nDISTINCT\nDO\nDROP\n"
                                "EACH\nELSE\nEND\nESCAPE\nEXCEPT\nEXCLUDE\nEXCLUSIVE\nEXISTS\nEXPLAIN\n"
                                "FAIL\nFILTER\nFIRST\nFOLLOWING\nFOR\nFOREIGN\nFROM\nFULL\nGENERATED\n"
                                "GLOB\nGROUP\nGROUPS\nHAVING\nIF\nIGNORE\nIMMEDIATE\nIN\nINDEX\nINDEXED\n"
                                "INITIALLY\nINNER\nINSERT\nINSTEAD\nINTERSECT\nINTO\nIS\nISNULL\nJOIN\n"
                                "KEY\nLAST\nLEFT\nLIKE\nLIMIT\nMATCH\nMATERIALIZED\nNATURAL\nNO\nNOT\n"
                                "NOTHING\nNOTNULL\nNULL\nNULLS\nOF\nOFFSET\nON\nOR\nORDER\nOTHERS\nOUTER\n"
                                "OVER\nPARTITION\nPLAN\nPRAGMA\nPRECEDING\nPRIMARY\nQUERY\nRAISE\n"
                                "RANGE\nRECURSIVE\nREFERENCES\nREGEXP\nREINDEX\nRELEASE\nRENAME\nREPLACE\n"
                                "RESTRICT\nRETURNING\nRIGHT\nROLLBACK\nROW\nROWS\nSAVEPOINT\n"
                                "SELECT\nSET\nTABLE\nTEMP\nTEMPORARY\nTHEN\nTIES\nTO\nTRANSACTION\nTRIGGER\n"
                                "UNBOUNDED\nUNION\nUNIQUE\nUPDATE\nUSING\nVACUUM\nVALUES\n"
                                "VIEW\nVIRTUAL\nWHEN\nWHERE\nWINDOW\nWITH\nWITHOUT\n";

constexpr auto lstKeywordsBash = "if\nthen\nelif\nelse\nfi\ntime\nfor\nin\nuntil\nwhile\ndo\ndone\ncase\nesac\ncoproc\nselect\nfunction\n";

constexpr auto lstKeywordsBatch = "VER\nASSOC\nCD\nCLS\nCOPY\nDEL\nDIR\nDATE\nECHO\nEXIT\nMD\nMOVE\nPATH\nPAUSE\nPROMPT\nRD\nREN\nREM\n"
                                  "START\nTIME\nTYPE\nVOL\nATTRIB\nCHKDSK\nCHOICE\nCMD\nCOMP\nCONVERT\nDRIVERQUERY\nEXPAND\nFIND\nFORMAT\n"
                                  "HELP\nIPCONFIG\nLABEL\nMORE\nNET\nPING\nSHUTDOWN\nSORT\nSUBST\nSYSTEMINFO\nTASKKILL\nTASKLIST\nXCOPY\nTREE\nFC\nDISKPART\nTITLE\nSET\n";

constexpr auto lstKeywordsHtml = "html\nbase\nhead\nlink\nmeta\nstyle\ntitle\nbody\naddress\narticle\naside\nfooter\nheader\nh1\nh2\nh3\nh4\nh5\nh6\nhgroup\nmain\nnav\nsection\nsearch\n"
                                 "blockquote\ndd\ndiv\ndl\ndt\nfigcaption\nfigure\nhr\nli\nmenu\nol\np\npre\nul\n"; // qqq incomplete

void QCodeEditor::setLexer( const enumCodeType typeCode )
{
    const auto lang = langFromCodeType( typeCode );
    auto       lex  = CreateLexer( lang );
    setILexer( (sptr_t) lex );

    m_lstKeywords = nullptr;
    switch ( typeCode ) {

    case enumCodeType::codeTypePython:
        m_lstKeywords = lstKeywordsPython;
        break;
    case enumCodeType::codeTypeSql:
        m_lstKeywords = lstKeywordsSql;
        break;
    case enumCodeType::codeTypeBash:
        m_lstKeywords = lstKeywordsBash;
        break;
    case enumCodeType::codeTypeBatch:
        m_lstKeywords = lstKeywordsBatch;
        break;
    case enumCodeType::codeTypeHtml:
        m_lstKeywords = lstKeywordsHtml;
        break;

    case enumCodeType::codeTypeMarkdown:
    case enumCodeType::codeTypeJson:
    case enumCodeType::codeTypeText:
    default:
        break;
    }

    if ( m_lstKeywords != nullptr ) {

        lex->WordListSet( 0, m_lstKeywords  );
    }
}

void QCodeEditor::setAnnotation( const int executionHandle, const QString sAnnotation, const bool bError )
{
    const auto line = getLineFromExecutionHandle( executionHandle );
    if ( line < 0 )
        return;

    if ( sAnnotation.isEmpty() ) {

        annotationSetText( line, nullptr );

    } else {

        // a little indent looks better but need it for each line
        constexpr auto newline = QChar{ '\n' };
        auto listLines = sAnnotation.split( newline, Qt::SkipEmptyParts );
        for ( auto& line : listLines ) {
            line = "\t" + line;
        }

        const auto sIndentedAnnotations = listLines.join( newline );
        annotationSetText( line, qUtf8Printable( sIndentedAnnotations ) );
        annotationSetStyle( line, bError ? STYLE_ANNOTATION_ERROR : STYLE_ANNOTATION_MEMORY );

    }
}

bool QCodeEditor::isFocal() const
{
    const auto activeWidget = qApp->focusWidget();
    const auto activeEditor = findParentOfType< QCodeEditor >( activeWidget );
    return ( activeEditor == this );
}

QString QCodeEditor::getLines( const sptr_t lineStart, const sptr_t lineEnd ) const
{
    auto listLines = QStringList{};
    for ( auto line = lineStart; line <= lineEnd; ++line ) {

        const auto sLine = getLine( line );
        listLines.append( sLine );
    }
    const auto sLines = listLines.join( QString{} );
    return sLines;
}

void QCodeEditor::onZoomActual()
{
    if ( ! isFocal() )
        return;

    setZoom( 0 );
}
void QCodeEditor::onZoomIn()
{
    if ( ! isFocal() )
        return;

    zoomIn();
}
void QCodeEditor::onZoomOut()
{
    if ( ! isFocal() )
        return;

    zoomOut();
}

void QCodeEditor::onEditCut()
{
    if ( ! isFocal() )
        return;

    cut();
}

void QCodeEditor::onEditCopy()
{
    if ( ! isFocal() )
        return;

    copy();
}

void QCodeEditor::onEditPaste()
{
    if ( ! isFocal() )
        return;

    paste();
}

void QCodeEditor::onEditSelectAll()
{
    if ( ! isFocal() )
        return;

    selectAll();
}


void QCodeEditor::addSourceText( const enumLineType lineType, const QString& sLine )
{
    constexpr auto CR           = QChar{ '\n' };
    const auto     sLineEndAdd  = sLine.endsWith( CR ) ? sLine : sLine + CR;
    const auto     linesBefore  = lineCount() - 1; // always reports extra line
    const auto     baLineEndAdd = sLineEndAdd.toUtf8();
    addText( baLineEndAdd.length(), baLineEndAdd.data() );
    const auto linesAfter = lineCount() - 1;
    for ( auto line = linesBefore; line < linesAfter; ++line ) {
        setLineType( line, lineType, true );
    }
}

void QCodeEditor::onEditToggleCellType()
{
    if ( ! isFocal() )
        return;

    const auto posStart  = selectionStart();
    const auto posEnd    = selectionEnd();

    auto lineStart = lineFromPosition( posStart );
    auto lineEnd   = lineFromPosition( posEnd );

    // if nothing selected, implicitly select the current line
    if ( lineStart == -1 || lineEnd == -1 ) {

        lineStart = currentLine();
        lineEnd   = lineStart;
    }

    for ( auto lineCurrent = lineStart; lineCurrent <= lineEnd; ++lineCurrent ) {
        toggleLineType( lineCurrent, true );
    }
}

enumLineType QCodeEditor::getLineType( const sptr_t line) const
{
    if ( m_codeType == enumCodeType::codeTypeMarkdown )
        return enumLineType::lineTypeMarkdown;

    const auto currentMarker = markerGet( line );

    if ( currentMarker & MASK_MARKDOWN_LINE )
        return enumLineType::lineTypeMarkdown;
    else
        return enumLineType::lineTypeCode;
}

void QCodeEditor::toggleLineType( const sptr_t line, const bool bWithUndo )
{
    const auto nCurrentLineType = getLineType( line );
    const auto lineTypeNew = ( nCurrentLineType == enumLineType::lineTypeMarkdown ) ? enumLineType::lineTypeCode : enumLineType::lineTypeMarkdown;
    setLineType( line, lineTypeNew, bWithUndo );
}

void QCodeEditor::setLineType( const sptr_t line, const enumLineType lineType, const bool bWithUndo )
{
    if ( m_codeType == enumCodeType::codeTypeMarkdown )
        return;

    if ( lineType == enumLineType::lineTypeMarkdown ) {

        markerAdd( line, MARKER_MARKDOWN_LINE );

    } else {

        markerDelete( line, MARKER_MARKDOWN_LINE );

    }

    marginSetText( line, nullptr );

    if ( bWithUndo ) {
        addUndoAction( line, UNDO_NONE );
    }
}

void QCodeEditor::onLinesAdded( Scintilla::Position /*linesAdded*/ )
{
    // ensure margin width for line numbers
    const auto nLines          = lineCount();
    const auto nPlaces         = ceil( log10( nLines ) ) + 1;
    const auto widthLineNumber = textWidth( STYLE_LINENUMBER, "9" ) * nPlaces;

    setMarginWidthN( MARGIN_NUMBER_LINENUMBER, widthLineNumber );
}

void QCodeEditor::autoComplete()
{
    // Try LSP completion first for Python files
    if ( m_codeType == enumCodeType::codeTypePython && m_lspClient && m_lspClient->isPythonLspReady() ) {
        autoCompleteLsp();
    } else {
        autoCompleteTraditional();
    }
}

void QCodeEditor::autoCompleteLsp()
{
    // Update document content if changed
    updateLspDocument();

    const auto posCurrent = currentPos();
    const auto lineCurrent = lineFromPosition( posCurrent );
    const auto lineStart = positionFromLine( lineCurrent );
    const auto colCurrent = posCurrent - lineStart;

    // Request completion from LSP server
    m_pendingCompletionRequestId = m_lspClient->requestPythonCompletion( m_filePath, lineCurrent, colCurrent );

    if ( m_pendingCompletionRequestId == -1 ) {
        // LSP request failed, fall back to traditional completion
        autoCompleteTraditional();
    }
}

void QCodeEditor::autoCompleteTraditional()
{
    // Clear any stored LSP completions since we're using traditional completion
    m_currentLspCompletions.clear();

    if ( ( m_lstKeywords == nullptr ) && (m_memory.isEmpty() ) )
        return;

    const auto posCurrent = currentPos();
    const auto posWordStart = wordStartPosition( posCurrent, true );
    const auto posEnd = posCurrent;
    const auto baWord = textRange( posWordStart, posCurrent );

    if (baWord.length() > 0 ) {

        auto lst = QByteArray{ m_lstKeywords };
        for ( const auto& iterCborMap : m_memory ) {

            const auto first = iterCborMap.first.toString();
            lst.append( first.toUtf8() );
            lst.append( "\n" );
        }
        for ( const auto& sFunction : m_listFunctions ) {

            lst.append( sFunction.toUtf8() );
            lst.append( "\n" );
        }
        autoCSetStyle( STYLE_CODE_COMPLETION );
        autoCSetSeparator( '\n' );
        autoCSetCaseInsensitiveBehaviour( SCI_AUTOCSETIGNORECASE );
        autoCShow( baWord.length(), lst.data() );
        autoCSetOrder(SC_ORDER_PERFORMSORT);
        autoCSetFillUps( "(." );
    }
}

void QCodeEditor::onCharAdded( int ch )
{
    const auto posCurrent        = currentPos();
    const auto lineCurrent       = lineFromPosition( posCurrent );

    const auto lineType = getLineType( lineCurrent );
    if ( lineType == enumLineType::lineTypeMarkdown )
        return;

    // is end of line?
    if ( ( ch == '\r' ) || (ch == '\n' ) )  {

        const auto lengthLineCurrent = lineLength( lineCurrent );

        // is new blank line with prior line?
        if  ( ( lineCurrent > 0 ) && ( lengthLineCurrent <= 2) ) {

            const auto sLinePrevious = getLine( lineCurrent - 1 );
            auto sLineNew = QByteArray{};

            // match prior indent
            const auto nPriorIndent = lineIndentation( lineCurrent - 1 );
            sLineNew.append( nPriorIndent, ' ' );

            // extra indent for python colon
            if ( (m_codeType == enumCodeType::codeTypePython) && (sLinePrevious.length() > 1) ) {

                if ( (sLinePrevious.right(2) == ":\n") || (sLinePrevious.right(2) == ":\r") ) {

                    sLineNew.append( PYTHON_INDENT_SPACES, ' ' );
                }
            }

            if ( sLineNew.length() > 0 ) {

                addText( sLineNew.length(), sLineNew.constData() );
            }

        }

    } else {

        autoComplete();

    }

}

void QCodeEditor::onLspReady()
{
    qDebug() << "LSP server is ready for Python completion";

    // If we have a file path and content, open the document with LSP
    if ( !m_filePath.isEmpty() ) {
        QString content = QString::fromUtf8( getText( length() ) );
        m_lspClient->openPythonDocument( m_filePath, content );
        m_lastContent = content;
    }
}

void QCodeEditor::onLspCompletionReceived( int requestId, const QList<Lsp::CompletionItem>& items )
{
    if ( requestId != m_pendingCompletionRequestId ) {
        return; // Not our request
    }

    m_pendingCompletionRequestId = -1;

    if ( items.isEmpty() ) {
        // No LSP completions, fall back to traditional
        m_currentLspCompletions.clear();
        autoCompleteTraditional();
        return;
    }

    // Show LSP completions
    const auto posCurrent = currentPos();
    const auto posWordStart = wordStartPosition( posCurrent, true );
    const auto wordLength = posCurrent - posWordStart;

    if (wordLength >= 0) {
        QList<Lsp::CompletionItem> sortedItems(items.size());
        std::partial_sort_copy(items.begin(), items.end(), sortedItems.begin(), sortedItems.end(), [](const Lsp::CompletionItem &a, const Lsp::CompletionItem &b)
        {
            return a.sortText < b.sortText;
        });

        // Store the sorted completion items for later insertText lookup
        m_currentLspCompletions = sortedItems;

        QByteArray completionList;
        for ( const auto& item : sortedItems ) {
            completionList.append(item.insertText.value_or(item.label).toUtf8());
            completionList.append("\n");
        }

        autoCSetStyle( STYLE_CODE_COMPLETION );
        autoCSetSeparator( '\n' );
        autoCSetCaseInsensitiveBehaviour( SCI_AUTOCSETIGNORECASE );
        autoCShow( wordLength, completionList.data() );
        autoCSetOrder(SC_ORDER_PRESORTED);
        autoCSetFillUps( "(." );
    }
}

void QCodeEditor::onLspError( const QString& message )
{
    qWarning() << "LSP Error:" << message;
}

void QCodeEditor::onAutoCompleteSelectionChange( Scintilla::Position position, const QString& text ) {
    // Only handle LSP completions, let traditional completions work normally
    if ( m_currentLspCompletions.isEmpty() || autoCCurrent() >= m_currentLspCompletions.size() ) {
        return; // Not an LSP completion, let Scintilla handle it normally
    }

    const auto item = m_currentLspCompletions[autoCCurrent()];

    if (item.insertText == "array") {
        qInfo() << "LSP completion selected:" << item.label << "insertText:" << item.insertText.value_or("");
    }

    // Extract documentation text from QVariant
    QString docText = item.label; // fallback to label
    if (item.documentation.has_value()) {
        const QVariant& doc = item.documentation.value();
        if (doc.userType() == QMetaType::QString) {
            docText = doc.toString();
        } else if (doc.canConvert<Lsp::MarkupContent>()) {
            docText = doc.value<Lsp::MarkupContent>().value();
        }
    }

    // QTimer::singleShot(500, this, [this, position, docText]() {
    //     callTipShow( position, docText.toUtf8().data() );
    // });
    callTipShow( position, docText.toUtf8().data() );
}

void QCodeEditor::onAutoCompleteSelection( Scintilla::Position position, const QString& text )
{
    // Only handle LSP completions, let traditional completions work normally
    if ( m_currentLspCompletions.isEmpty() ) {
        return; // Not an LSP completion, let Scintilla handle it normally
    }

    QString insertTextToUse = text; // Default to the label text
    const auto item = m_currentLspCompletions[autoCCurrent()];

    // Use insertText if available, otherwise fall back to label
    if ( item.insertText.has_value() && !item.insertText.value().isEmpty() ) {
        insertTextToUse = item.insertText.value();
    }

    // If we found a different insertText, we need to replace what Scintilla would insert
    if ( insertTextToUse != text ) {
        // Cancel the default autocompletion to prevent double insertion
        autoCCancel();

        // Calculate the range to replace
        const auto posCurrent = currentPos();
        const auto posWordStart = wordStartPosition( posCurrent, true );
        const auto wordLength = posCurrent - posWordStart;

        // Replace the partial word with the insertText
        setTargetRange( posWordStart, posCurrent );
        replaceTarget( insertTextToUse.toUtf8().length(), insertTextToUse.toUtf8().data() );

        // Position cursor at the end of the inserted text
        const auto newPos = posWordStart + insertTextToUse.length();
        setSel( newPos, newPos );
    }

    // Clear the stored completions since the completion is done
    m_currentLspCompletions.clear();
}

void QCodeEditor::updateLspDocument()
{
    if ( !m_lspClient || !m_lspClient->isPythonLspReady() || m_filePath.isEmpty() ) {
        return;
    }

    QString currentContent = QString::fromUtf8( getText( length() ) );

    if ( currentContent != m_lastContent ) {
        m_lspClient->updatePythonDocument( m_filePath, currentContent, 0 ); // Let LSP client handle versioning
        m_lastContent = currentContent;
    }
}

bool QCodeEditor::memoryVisible() const
{
    return annotationVisible();
}

void QCodeEditor::toggleMemoryVisible()
{
    setMemoryVisible( ! memoryVisible() );
}

void QCodeEditor::setMemoryVisible(const bool bVisible)
{
    annotationSetVisible( bVisible ? ANNOTATION_STANDARD : ANNOTATION_HIDDEN );

    auto mainWindow = QTrySailMainWindow::getTrysailMainWindow();
    mainWindow->actionViewToggleMemoryVisible->setChecked( bVisible );

    // // fold testing
    // if ( ! bVisible ) {
    //     eOLAnnotationSetVisible( EOLANNOTATION_BOXED );
    //     for ( auto line = 0; line < lineCount(); ++line ) {
    //         const auto nfoldLevel = foldLevel( line ) &
    //         SC_FOLDLEVELNUMBERMASK; const auto nFoldHeader = foldLevel( line
    //         ) & SC_FOLDLEVELHEADERFLAG; eOLAnnotationSetText( line,
    //         u"fold=%1: header=%2"_s.arg( nfoldLevel ).arg( nFoldHeader
    //         ).toUtf8().data()
    //         );
    //     }
    // }
}

sptr_t QCodeEditor::currentLine() const
{
    const auto posCurrent  = currentPos();
    const auto lineCurrent = lineFromPosition( posCurrent );
    return lineCurrent;
}

std::tuple< sptr_t, sptr_t > QCodeEditor::findChunkFromLine( const sptr_t lineFocal ) const
{
    const auto chunkStart = findStartFromLine( lineFocal );
    const auto chunkEnd   = findEndFromLine( lineFocal );
    return std::make_tuple( chunkStart, chunkEnd );
}

sptr_t QCodeEditor::findStartFromLine( const sptr_t lineFocal ) const
{
    auto lineStart = lineFocal;

    const auto lineTypeFocal = getLineType( lineFocal );

    if ( lineTypeFocal == enumLineType::lineTypeMarkdown ) {

        // go up to start of markdown
        while ( lineStart >= 0 ) {
            const auto linePrior      = lineStart - 1;
            const auto nPriorLineType = getLineType( linePrior );
            if ( nPriorLineType != lineTypeFocal )
                break;

            lineStart--;
        }

    } else {

        switch ( m_codeType ) {

        case enumCodeType::codeTypePython: {

                while ( lineStart > 0 ) {
                    // change line type always ends block
                    const auto priorType = getLineType( lineStart - 1 );
                    if ( priorType != lineTypeFocal )
                        break;

                    // continuation lines always attach to prior
                    const auto baCode = getLine( lineStart );
                    if ( QPythonEngine::lineIsContinuation( baCode ) ) {
                        lineStart--;
                        continue;
                    }

                    // would this line fold?
                    const auto foldStartLevel = foldLevel( lineStart ) & SC_FOLDLEVELNUMBERMASK;
                    if ( foldStartLevel == SC_FOLDLEVELBASE )
                        break;

                    lineStart--;
                }
            }
            break;

        case enumCodeType::codeTypeSql: {

                // go to prior semicolon
                while ( lineStart > 0 ) {
                    const auto baCode = getLine( lineStart - 1 );
                    const auto sCode  = QString::fromUtf8( baCode );
                    if ( !sCode.contains( u";"_s ) )
                        break;

                    lineStart--;
                }
            }
            break;

        case enumCodeType::codeTypeBash:
        case enumCodeType::codeTypeHtml:
        case enumCodeType::codeTypeJson:
        case enumCodeType::codeTypeBatch:
        case enumCodeType::codeTypeText:
        case enumCodeType::codeTypeMarkdown: {

                // go to begin
                lineStart = 1;
            }
            break;

        default:
            break;
        }
    }
    return lineStart;
}

sptr_t QCodeEditor::findEndFromLine( const sptr_t lineFocal ) const
{
    auto lineEnd = lineFocal;

    const auto lineTypeFocal = getLineType( lineFocal );

    if ( lineTypeFocal == enumLineType::lineTypeMarkdown ) {

        // go up to end of markdown
        while ( lineEnd < lineCount() ) {

            const auto lineNext     = lineEnd + 1;
            const auto typeNextLine = getLineType( lineNext );
            if ( typeNextLine != lineTypeFocal )
                break;

            lineEnd++;
        }

    } else {

        switch ( m_codeType ) {

        case enumCodeType::codeTypePython: {

                for ( auto lineNext = lineEnd + 1; lineNext < lineCount(); ++lineNext ) {
                    // change line type always ends block
                    const auto typeNextLine = getLineType( lineNext );
                    if ( typeNextLine != lineTypeFocal )
                        break;

                    // is next line a continuation line?
                    const auto baCode = getLine( lineEnd + 1 );
                    if ( QPythonEngine::lineIsContinuation( baCode ) ) {
                        lineEnd++;
                        continue;
                    }

                    // does next line start a new fold?
                    const auto foldNext = foldLevel( lineNext ) & SC_FOLDLEVELNUMBERMASK;
                    if ( foldNext == SC_FOLDLEVELBASE )
                        break;

                    lineEnd = lineNext;
                }
            }
            break;

        case enumCodeType::codeTypeSql: {

                // go to next semicolon
                while ( lineEnd < lineCount() ) {
                    const auto baCode = getLine( lineEnd );
                    const auto sCode  = QString::fromUtf8( baCode );
                    if ( sCode.contains( u";"_s ) ) {
                        break;
                    }

                    lineEnd++;
                }

            }
            break;

        case enumCodeType::codeTypeBash:
        case enumCodeType::codeTypeJson:
        case enumCodeType::codeTypeHtml:
        case enumCodeType::codeTypeBatch:
        case enumCodeType::codeTypeText:
        case enumCodeType::codeTypeMarkdown: {

                // go to end
                lineEnd = lineCount() - 1;
            }
            break;

        default:
            break;
        }
    }
    return lineEnd;
}

void QCodeEditor::resetAllPendingExecutionStatus()
{
    for ( auto line = 0; line < lineCount(); ++line ) {

        const auto lineStatus = marginStyle( line );
        if ( lineStatus == STYLE_EXECUTION_REQUESTED ) {

            marginSetStyle( line, STYLE_EXECUTION_NONE );
            marginSetText( line, nullptr );
        }
    }
}

void QCodeEditor::setExecutionStatus( const int executionHandle, const enumExecutionStatus statusExecutation )
{
    const auto line = getLineFromExecutionHandle( executionHandle );
    if ( line < 0 )
        return;

    switch ( statusExecutation ) {

    case enumExecutionStatus::executionStatusRequested:
        marginSetStyle( line, STYLE_EXECUTION_REQUESTED );
        marginSetText( line, " …" );//⁃ ⃛
        break;

    case enumExecutionStatus::executionStatusExecuting:
        marginSetStyle( line, STYLE_EXECUTION_EXECUTING );
        marginSetText( line, " ↻" );//*‣➤⏵
        break;

    case enumExecutionStatus::executionStatusDoneOk:
        marginSetStyle( line, STYLE_EXECUTION_DONE_OK );
        marginSetText( line, " ✔" );//◉⬤●✓
        break;

    case enumExecutionStatus::executionStatusDoneError:
        marginSetStyle( line, STYLE_EXECUTION_DONE_ERROR );
        marginSetText( line, " ✘" );
        break;

    case enumExecutionStatus::executionStatusNone:
    default:
        marginSetStyle( line, STYLE_EXECUTION_NONE );
        marginSetText( line, nullptr );
        break;
    }
}

int QCodeEditor::getExecutionHandleFromLine( const sptr_t nLine )
{
    constexpr auto MAX_MARKERS = 10;

    for ( auto which = 0; which < MAX_MARKERS; ++which ) {

        const auto numberMarker = markerNumberFromLine( nLine, which );

        if ( numberMarker == -1 )
            break;

        if ( numberMarker == MARKER_EXECUTION_STATUS ) {

            const auto executionHandle = markerHandleFromLine( nLine, which );
            return executionHandle;
        }
    }
    return markerAdd( nLine, MARKER_EXECUTION_STATUS );
}

sptr_t QCodeEditor::getLineFromExecutionHandle( const int executionHandle )
{
    const auto line = markerLineFromHandle( executionHandle );
    return line;
}


void QCodeEditor::showContextMenu( const QPoint& pos )
{
    auto mainWindow = QTrySailMainWindow::getTrysailMainWindow();

    auto menu = QMenu{ this };
    menu.setToolTipsVisible( true );
    menu.addAction( mainWindow->actionEditToggleCellType );
    menu.addSeparator();
    menu.addAction( mainWindow->actionCodeRunStart );
    menu.addAction( mainWindow->actionCodeRunStop );
    menu.addAction( mainWindow->actionCodeRunRestart );
    menu.addAction( mainWindow->actionCodeRunRestartRunAll );
    menu.addSeparator();
    menu.addAction( mainWindow->actionEditZoomActual );
    menu.addAction( mainWindow->actionEditZoomIn );
    menu.addAction( mainWindow->actionEditZoomOut );
    menu.addSeparator();
    menu.addAction( mainWindow->actionEditUndo );
    menu.addAction( mainWindow->actionEditRedo );
    menu.addSeparator();
    menu.addAction( mainWindow->actionEditCut );
    menu.addAction( mainWindow->actionEditCopy );
    menu.addAction( mainWindow->actionEditPaste );
    menu.addSeparator();
    menu.addAction( mainWindow->actionEditSelectAll );

    const auto posStart  = selectionStart();
    const auto posEnd    = selectionEnd();

    // if nothing selected, implicitly select the current line
    if ( posStart == posEnd ) {

        const auto posCurrent = positionFromPoint( pos.x(), pos.y() );
        gotoPos( posCurrent );
    }

    const auto posGlobal = mapToGlobal( pos );
    menu.exec( posGlobal );
}
